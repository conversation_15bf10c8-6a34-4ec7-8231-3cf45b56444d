{"version": 3, "file": "array-I1yfCXUO.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/coercion/array.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Wraps the provided value in an array, unless the provided value is an array. */\nexport function coerceArray<T>(value: T | T[]): T[];\nexport function coerceArray<T>(value: T | readonly T[]): readonly T[];\nexport function coerceArray<T>(value: T | T[]): T[] {\n  return Array.isArray(value) ? value : [value];\n}\n"], "names": [], "mappings": "AAWM,SAAU,WAAW,CAAI,KAAc,EAAA;AAC3C,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC;AAC/C;;;;"}