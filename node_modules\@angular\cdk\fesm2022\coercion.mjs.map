{"version": 3, "file": "coercion.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/coercion/boolean-property.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/coercion/string-array.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Type describing the allowed values for a boolean input.\n * @docs-private\n */\nexport type BooleanInput = string | boolean | null | undefined;\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nexport function coerceBooleanProperty(value: any): boolean {\n  return value != null && `${value}` !== 'false';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nexport function coerceStringArray(value: any, separator: string | RegExp = /\\s+/): string[] {\n  const result = [];\n\n  if (value != null) {\n    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n    for (const sourceValue of sourceValues) {\n      const trimmedString = `${sourceValue}`.trim();\n      if (trimmedString) {\n        result.push(trimmedString);\n      }\n    }\n  }\n\n  return result;\n}\n"], "names": [], "mappings": ";;;;;AAcA;AACM,SAAU,qBAAqB,CAAC,KAAU,EAAA;IAC9C,OAAO,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,CAAA,CAAE,KAAK,OAAO;AAChD;;ACTA;;;;;;;;;;;;;;;;AAgBG;SACa,iBAAiB,CAAC,KAAU,EAAE,YAA6B,KAAK,EAAA;IAC9E,MAAM,MAAM,GAAG,EAAE;AAEjB,IAAA,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAA,EAAG,KAAK,CAAA,CAAE,CAAC,KAAK,CAAC,SAAS,CAAC;AAC/E,QAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;YACtC,MAAM,aAAa,GAAG,CAAG,EAAA,WAAW,EAAE,CAAC,IAAI,EAAE;YAC7C,IAAI,aAAa,EAAE;AACjB,gBAAA,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;;;;AAKhC,IAAA,OAAO,MAAM;AACf;;;;"}