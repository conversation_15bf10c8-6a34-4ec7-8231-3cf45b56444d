<div class="flex w-full message-wrapper" [ngClass]="{
    'justify-end': message.isOutgoing,
    'justify-start': !message.isOutgoing && message.type !== 'poll',
    'justify-center': message.type === 'poll'
}">
    <div class="flex items-center space-x-2 max-w-[80%]"
        [ngClass]="{'flex-row-reverse space-x-reverse': message.isOutgoing}">
        <div class="relative flex flex-col"
            [ngClass]="{'items-end': message.isOutgoing, 'items-start': !message.isOutgoing, 'w-full': message.type === 'poll'}">
            <div [ngClass]="{
            'message-bubble': message.type !== 'poll',
            'poll-bubble': message.type === 'poll',
            'outgoing': message.isOutgoing,
            'incoming': !message.isOutgoing,
            'p-0 overflow-hidden': message.type === 'image' || message.type === 'gif',
            'p-3': message.type !== 'image' && message.type !== 'gif' && message.type !== 'poll'
        }">
                <ng-container [ngSwitch]="message.type">
                    <ng-container *ngSwitchCase="'text'">
                        <p *ngIf="message.type === 'text'" class="text-sm break-words">{{ message.text }}</p>
                    </ng-container>
                    <ng-container *ngSwitchCase="'image'">
                        <div *ngIf="message.type === 'image'" class="image-grid"
                            [class.single]="message.imageUrls.length === 1">
                            <!-- Thêm (click) event -->
                            <img *ngFor="let url of message.imageUrls; let i = index" [src]="url"
                                (click)="onImageClick(i)" alt="Sent image" class="cursor-pointer" />
                        </div>
                    </ng-container>
                    <ng-container *ngSwitchCase="'file'">
                        <div *ngIf="message.type === 'file'" class="flex items-center gap-3">
                            <i class="fa-solid fa-file text-2xl"></i>
                            <div class="text-sm">
                                <p class="font-medium break-all">{{ message.fileInfo.name }}</p>
                                <p class="text-xs opacity-80">{{ message.fileInfo.size }}</p>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngSwitchCase="'audio'">
                        <div *ngIf="message.type === 'audio'" class="flex items-center gap-2 w-64">
                            <audio [src]="message.audioUrl" controls class="w-full"></audio>
                        </div>
                    </ng-container>
                    <ng-container *ngSwitchCase="'poll'">
                        <div *ngIf="message.type === 'poll'">
                            <p class="font-bold mb-3 text-center">{{ message.pollData.question }}</p>
                            <div class="flex flex-col gap-2">
                                <button *ngFor="let option of message.pollData.options; let optIndex = index"
                                    (click)="onVote(optIndex)" class="poll-option"
                                    [class.voted]="option.voters.includes(currentUser.id.toString())">
                                    <div class="flex justify-between items-center w-full">
                                        <span>{{ option.text }}</span><span class="font-semibold">{{ option.votes
                                            }}</span>
                                    </div>
                                    <div class="poll-bar" [style.width.%]="getPollPercentage(option, message.pollData)">
                                    </div>
                                </button>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngSwitchCase="'gif'">
                        <img *ngIf="message.type === 'gif'" [src]="message.gifUrl" alt="Selected GIF"
                            class="max-w-xs rounded-lg block" />
                    </ng-container>
                </ng-container>
            </div>
            <!-- Reaction is a sibling to the bubble, but inside the relative wrapper -->
            <div *ngIf="message.reaction" class="reaction-display"
                [ngClass]="{'left-2': message.isOutgoing, 'right-2': !message.isOutgoing}">
                {{ message.reaction }}
            </div>
        </div>
        <!-- Action buttons -->
        <div *ngIf="message.type !== 'poll'" class="message-actions"
            [ngClass]="{'flex-row-reverse': message.isOutgoing}">
            <button (click)="openReactionPicker.emit($event)" class="action-button"><i
                    class="fa-regular fa-face-smile"></i></button>
            <button (click)="openOptionsMenu.emit($event)" class="action-button"><i
                    class="fa-solid fa-ellipsis-vertical"></i></button>
        </div>
    </div>
</div>