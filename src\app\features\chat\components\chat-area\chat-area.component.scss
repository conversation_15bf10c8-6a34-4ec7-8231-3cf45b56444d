:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.chat-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden; // Giữ overflow ở đâ<PERSON> th<PERSON>, vì nó chỉ ảnh hưởng bên trong
}

app-chat-header,
app-chat-input {
  flex-shrink: 0;
  position: relative; // Thêm position relative để popup định vị đúng
  z-index: 10;
}

.message-list-container {
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
  background-color: #f0f2f5;
  padding: 1rem;
}
// Các style cho modal giữ nguyên như cũ
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.modal-content {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
}
.reaction-picker {
  padding: 0.5rem !important;
  display: flex;
  gap: 0.5rem;
  width: auto !important;
}
.reaction-emoji {
  font-size: 1.75rem;
  cursor: pointer;
  padding: 0.25rem;
  transition: transform 0.1s ease-in-out;
  &:hover {
    transform: scale(1.2);
  }
}
.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  border: none;
  background-color: transparent;
  &:hover {
    background-color: #f0f2f5;
  }
  &.danger {
    color: #d93025;
  }
}
.gif-modal {
  max-width: min(500px, 90vw);
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  h3 {
    flex-shrink: 0;
  }

  input {
    flex-shrink: 0;
  }

  .gif-grid {
    flex: 1;
    overflow-y: auto;
  }

  // Mobile responsive
  @media (max-width: 768px) {
    max-width: 95vw;
    max-height: 70vh;
    padding: 1rem;
  }
}

.gif-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.5rem;
  max-height: 50vh;
  overflow-y: auto;

  // Mobile responsive
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.375rem;
    max-height: 40vh;
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.25rem;
  }
}
.poll-creation-modal {
  max-width: 500px;
}
