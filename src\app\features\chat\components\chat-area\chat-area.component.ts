import {
  Component,
  On<PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Services
import {
  ChatService,
  ChatUser,
  ChatMessage,
  NewMessagePayload,
} from '../../chat.service';
import { NotificationService } from '../../../../shared/services/notification.service';
import { ThemeService } from '../../../../shared/services/theme.service';
import { GifService, GifResult } from '../../services/gif.service';

// Child Components
import { ChatHeaderComponent } from '../chat-header/chat-header.component';
import { ChatMessageComponent } from '../chat-message/chat-message.component';
import { ChatInputComponent } from '../chat-input/chat-input.component';
import { ImageViewerComponent } from '../image-viewer/image-viewer.component';

@Component({
  selector: 'app-chat-area',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ChatHeaderComponent,
    ChatMessageComponent,
    ChatInputComponent,
    ImageViewerComponent,
  ],
  templateUrl: './chat-area.component.html',
  styleUrls: ['./chat-area.component.scss'],
})
export class ChatAreaComponent implements OnInit, OnDestroy {
  // --- STATE MANAGEMENT ---
  selectedUser$!: Observable<ChatUser | null>;
  currentUser!: ChatUser;
  messages: ChatMessage[] = [];
  themeColor$: Observable<string>;

  // --- UI VISIBILITY MANAGEMENT ---
  public isOptionsMenuModalVisible = false;
  public isPollModalVisible = false;
  public isGifModalVisible = false;
  public pickerOpenForMessageIndex: number | null = null;
  public selectedMessageForOptions: ChatMessage | null = null;

  // --- MODAL DATA ---
  pollQuestion: string = '';
  pollOptions: { text: string }[] = [{ text: '' }, { text: '' }];
  gifSearchTerm: string = '';
  gifResults: GifResult[] = [];
  isLoadingGifs: boolean = false;

  // --- AUDIO RECORDING ---
  isRecording: boolean = false;
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];

  // --- IMAGE VIEWER ---
  isImageViewerVisible: boolean = false;
  imageViewerImages: string[] = [];
  imageViewerCurrentIndex: number = 0;

  private destroy$ = new Subject<void>();

  @Output() toggleInfoSidebar = new EventEmitter<void>();

  constructor(
    private chatService: ChatService,
    private notificationService: NotificationService,
    private themeService: ThemeService,
    private gifService: GifService
  ) {
    this.themeColor$ = this.themeService.activeColor$;
  }

  @HostListener('document:click')
  onDocumentClick(): void {
    this.pickerOpenForMessageIndex = null;
  }

  ngOnInit(): void {
    this.selectedUser$ = this.chatService.getSelectedUser();
    this.selectedUser$.pipe(takeUntil(this.destroy$)).subscribe((user) => {
      if (user) {
        this.currentUser = user;
        this.messages = this.chatService.getMessagesForUser(user.id);
      } else {
        this.currentUser = {
          id: 0,
          name: 'Me',
          avatar: '',
          active: true,
          lastMessage: '',
          time: '',
        };
        this.messages = [];
      }
    });
  }
  addPollOption(): void {
    if (this.pollOptions.length < 10) {
      this.pollOptions.push({ text: '' });
    } else {
      this.notificationService.show('Maximum of 10 options reached.', 'error');
    }
  }
  removePollOption(index: number): void {
    if (this.pollOptions.length > 2) {
      this.pollOptions.splice(index, 1);
    }
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // --- EVENT HANDLERS ---
  handleSendMessage(text: string): void {
    this.addMessage({ type: 'text', text });
  }
  handleSendFile(file: File): void {
    if (file.size > 25 * 1024 * 1024) {
      this.notificationService.show('File is too large (max 25MB)', 'error');
      return;
    }
    this.addMessage({
      type: 'file',
      fileInfo: {
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        type: file.type,
      },
    });
  }
  handleSendImages(files: FileList): void {
    const imageUrls: string[] = [];
    Array.from(files).forEach((file) => {
      if (file.type.startsWith('image/') && file.size <= 15 * 1024 * 1024) {
        imageUrls.push(URL.createObjectURL(file));
      } else if (file.size > 15 * 1024 * 1024) {
        this.notificationService.show(
          `Image '${file.name}' is too large (max 15MB)`,
          'error'
        );
      }
    });
    if (imageUrls.length > 0) {
      this.addMessage({ type: 'image', imageUrls });
    }
  }
  async handleRecordAudio(): Promise<void> {
    if (this.isRecording) {
      this.mediaRecorder?.stop();
    } else {
      if (!navigator.mediaDevices?.getUserMedia) {
        this.notificationService.show(
          'Audio recording is not supported.',
          'error'
        );
        return;
      }
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        this.isRecording = true;
        this.notificationService.show('Recording started...', 'info');
        this.mediaRecorder = new MediaRecorder(stream);
        this.audioChunks = [];
        this.mediaRecorder.ondataavailable = (event) =>
          this.audioChunks.push(event.data);
        this.mediaRecorder.onstop = () => {
          this.isRecording = false;
          const audioBlob = new Blob(this.audioChunks, { type: 'audio/mp4' });
          const audioUrl = URL.createObjectURL(audioBlob);
          this.addMessage({ type: 'audio', audioUrl });
          stream.getTracks().forEach((track) => track.stop());
        };
        this.mediaRecorder.start();
      } catch (err) {
        this.notificationService.show(
          'Could not start recording. Please grant permission.',
          'error'
        );
        this.isRecording = false;
      }
    }
  }
  handleVote(event: { messageId: number; optionIndex: number }): void {
    this.chatService.voteOnPoll(
      this.currentUser.id,
      event.messageId,
      event.optionIndex
    );
  }
  handleOpenReactionPicker(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.pickerOpenForMessageIndex =
      this.pickerOpenForMessageIndex === index ? null : index;
  }
  handleOpenOptionsMenu(message: ChatMessage, event: MouseEvent): void {
    event.stopPropagation();
    this.selectedMessageForOptions = message;
    this.isOptionsMenuModalVisible = true;
  }

  // --- CORE LOGIC ---
  private addMessage(payload: NewMessagePayload): void {
    this.chatService.addMessage(this.currentUser.id, payload);
    this.messages = this.chatService.getMessagesForUser(this.currentUser.id);
  }
  selectReaction(messageIndex: number, reaction: string): void {
    this.messages[messageIndex].reaction = reaction;
    this.pickerOpenForMessageIndex = null;
  }
  createPoll(): void {
    if (
      this.pollQuestion.trim() &&
      this.pollOptions.every((opt) => opt.text.trim())
    ) {
      this.addMessage({
        type: 'poll',
        pollData: {
          question: this.pollQuestion,
          options: this.pollOptions.map((opt) => ({
            text: opt.text,
            votes: 0,
            voters: [],
          })),
        },
      });
      this.closePollModal();
    } else {
      this.notificationService.show(
        'Please enter a question and all options.',
        'error'
      );
    }
  }
  searchGifs(): void {
    if (!this.gifSearchTerm.trim()) {
      // Load trending GIFs when search is empty
      this.loadTrendingGifs();
      return;
    }

    this.isLoadingGifs = true;
    this.gifService
      .searchGifs(this.gifSearchTerm, 20)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (gifs) => {
          this.gifResults = gifs;
          this.isLoadingGifs = false;
        },
        error: (error) => {
          console.error('Error searching GIFs:', error);
          this.notificationService.show(
            'Không thể tải GIF. Vui lòng thử lại.',
            'error'
          );
          this.isLoadingGifs = false;
        },
      });
  }

  private loadTrendingGifs(): void {
    this.isLoadingGifs = true;
    this.gifService
      .getTrendingGifs(20)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (gifs) => {
          this.gifResults = gifs;
          this.isLoadingGifs = false;
        },
        error: (error) => {
          console.error('Error loading trending GIFs:', error);
          this.isLoadingGifs = false;
        },
      });
  }

  selectGif(gif: GifResult): void {
    this.addMessage({ type: 'gif', gifUrl: gif.url });
    this.closeGifModal();
  }
  deleteMessage(): void {
    if (!this.selectedMessageForOptions) return;
    this.chatService.deleteMessage(
      this.currentUser.id,
      this.selectedMessageForOptions.id
    );
    this.messages = this.chatService.getMessagesForUser(this.currentUser.id);
    this.closeOptionsMenuModal();
  }

  // --- MODAL TOGGLES ---
  openPollModal(): void {
    this.isPollModalVisible = true;
  }
  closePollModal(): void {
    this.isPollModalVisible = false;
  }
  openGifModal(): void {
    this.isGifModalVisible = true;
    this.gifSearchTerm = '';
    // Load trending GIFs when modal opens
    this.loadTrendingGifs();
  }
  closeGifModal(): void {
    this.isGifModalVisible = false;
  }
  closeOptionsMenuModal(): void {
    this.isOptionsMenuModalVisible = false;
    this.selectedMessageForOptions = null;
  }
  forwardMessage(): void {
    this.closeOptionsMenuModal();
  }

  // --- IMAGE VIEWER METHODS ---
  handleImageClick(event: { imageUrls: string[]; startIndex: number }): void {
    this.imageViewerImages = event.imageUrls;
    this.imageViewerCurrentIndex = event.startIndex;
    this.isImageViewerVisible = true;
  }

  closeImageViewer(): void {
    this.isImageViewerVisible = false;
    this.imageViewerImages = [];
    this.imageViewerCurrentIndex = 0;
  }

  onImageViewerIndexChange(index: number): void {
    this.imageViewerCurrentIndex = index;
  }
}
