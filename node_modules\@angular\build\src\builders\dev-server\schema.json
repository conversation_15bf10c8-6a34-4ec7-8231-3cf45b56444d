{"$schema": "http://json-schema.org/draft-07/schema", "title": "Dev Server Target", "description": "Dev Server target options for Build Facade.", "type": "object", "properties": {"buildTarget": {"type": "string", "description": "A build builder target to serve in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "port": {"type": "number", "description": "Port to listen on.", "default": 4200}, "host": {"type": "string", "description": "Host to listen on.", "default": "localhost"}, "proxyConfig": {"type": "string", "description": "Proxy configuration file. For more information, see https://angular.dev/tools/cli/serve#proxying-to-a-backend-server."}, "ssl": {"type": "boolean", "description": "Serve using HTTPS.", "default": false}, "sslKey": {"type": "string", "description": "SSL key to use for serving HTTPS."}, "sslCert": {"type": "string", "description": "SSL certificate to use for serving HTTPS."}, "allowedHosts": {"description": "The hosts that the development server will respond to. This option sets the Vite option of the same name. For further details: https://vite.dev/config/server-options.html#server-allowedhosts", "default": [], "oneOf": [{"type": "array", "description": "A list of hosts that the development server will respond to.", "items": {"type": "string"}}, {"type": "boolean", "description": "Indicates that all hosts are allowed. This is not recommended and a security risk."}]}, "headers": {"type": "object", "description": "Custom HTTP headers to be added to all responses.", "propertyNames": {"pattern": "^[-_A-Za-z0-9]+$"}, "additionalProperties": {"type": "string"}}, "open": {"type": "boolean", "description": "Opens the url in default browser.", "default": false, "alias": "o"}, "verbose": {"type": "boolean", "description": "Adds more details to output logging."}, "liveReload": {"type": "boolean", "description": "Whether to reload the page on change, using live-reload.", "default": true}, "servePath": {"type": "string", "description": "The pathname where the application will be served."}, "hmr": {"type": "boolean", "description": "Enable hot module replacement. Defaults to the value of 'liveReload'. Currently, only global and component stylesheets are supported."}, "watch": {"type": "boolean", "description": "Rebuild on change.", "default": true}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "inspect": {"default": false, "description": "Activate debugging inspector. This option only has an effect when 'SSR' or 'SSG' are enabled.", "oneOf": [{"type": "string", "description": "Activate the inspector on host and port in the format of `[[host:]port]`. See the security warning in https://nodejs.org/docs/latest-v22.x/api/cli.html#warning-binding-inspector-to-a-public-ipport-combination-is-insecure regarding the host parameter usage."}, {"type": "boolean"}]}, "prebundle": {"description": "Enable and control the Vite-based development server's prebundling capabilities. To enable prebundling, the Angular CLI cache must also be enabled.", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"exclude": {"description": "List of package imports that should not be prebundled by the development server. The packages will be bundled into the application code itself. Note: specifying `@foo/bar` marks all paths within the `@foo/bar` package as excluded, including sub-paths like `@foo/bar/baz`.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["exclude"]}]}}, "additionalProperties": false, "required": ["buildTarget"]}