{"version": 3, "file": "accordion.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/accordion/accordion.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/accordion/accordion-item.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/accordion/accordion-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {_IdGenerator} from '../a11y';\nimport {Subject} from 'rxjs';\n\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nexport const CDK_ACCORDION = new InjectionToken<CdkAccordion>('CdkAccordion');\n\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\n@Directive({\n  selector: 'cdk-accordion, [cdkAccordion]',\n  exportAs: 'cdkAccordion',\n  providers: [{provide: CDK_ACCORDION, useExisting: CdkAccordion}],\n})\nexport class CdkAccordion implements On<PERSON><PERSON>roy, OnChanges {\n  /** Emits when the state of the accordion changes */\n  readonly _stateChanges = new Subject<SimpleChanges>();\n\n  /** Stream that emits true/false when openAll/closeAll is triggered. */\n  readonly _openCloseAllActions: Subject<boolean> = new Subject<boolean>();\n\n  /** A readonly id value to use for unique selection coordination. */\n  readonly id: string = inject(_IdGenerator).getId('cdk-accordion-');\n\n  /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n  @Input({transform: booleanAttribute}) multi: boolean = false;\n\n  /** Opens all enabled accordion items in an accordion where multi is enabled. */\n  openAll(): void {\n    if (this.multi) {\n      this._openCloseAllActions.next(true);\n    }\n  }\n\n  /** Closes all enabled accordion items. */\n  closeAll(): void {\n    this._openCloseAllActions.next(false);\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    this._stateChanges.next(changes);\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._openCloseAllActions.complete();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Output,\n  Directive,\n  EventEmitter,\n  Input,\n  OnDestroy,\n  ChangeDetectorRef,\n  booleanAttribute,\n  inject,\n  OnInit,\n  signal,\n} from '@angular/core';\nimport {_IdGenerator} from '../a11y';\nimport {UniqueSelectionDispatcher} from '../collections';\nimport {CDK_ACCORDION, CdkAccordion} from './accordion';\nimport {Subscription} from 'rxjs';\n\n/**\n * A basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\n@Directive({\n  selector: 'cdk-accordion-item, [cdkAccordionItem]',\n  exportAs: 'cdkAccordionItem',\n  providers: [\n    // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n    // registering to the same accordion.\n    {provide: CDK_ACCORDION, useValue: undefined},\n  ],\n})\nexport class CdkAccordionItem implements OnInit, OnDestroy {\n  accordion = inject<CdkAccordion>(CDK_ACCORDION, {optional: true, skipSelf: true})!;\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  protected _expansionDispatcher = inject(UniqueSelectionDispatcher);\n\n  /** Subscription to openAll/closeAll events. */\n  private _openCloseAllSubscription = Subscription.EMPTY;\n  /** Event emitted every time the AccordionItem is closed. */\n  @Output() readonly closed: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted every time the AccordionItem is opened. */\n  @Output() readonly opened: EventEmitter<void> = new EventEmitter<void>();\n  /** Event emitted when the AccordionItem is destroyed. */\n  @Output() readonly destroyed: EventEmitter<void> = new EventEmitter<void>();\n\n  /**\n   * Emits whenever the expanded state of the accordion changes.\n   * Primarily used to facilitate two-way binding.\n   * @docs-private\n   */\n  @Output() readonly expandedChange: EventEmitter<boolean> = new EventEmitter<boolean>();\n\n  /** The unique AccordionItem id. */\n  readonly id: string = inject(_IdGenerator).getId('cdk-accordion-child-');\n\n  /** Whether the AccordionItem is expanded. */\n  @Input({transform: booleanAttribute})\n  get expanded(): boolean {\n    return this._expanded;\n  }\n  set expanded(expanded: boolean) {\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  private _expanded = false;\n\n  /** Whether the AccordionItem is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled() {\n    return this._disabled();\n  }\n  set disabled(value: boolean) {\n    this._disabled.set(value);\n  }\n  private _disabled = signal(false);\n\n  /** Unregister function for _expansionDispatcher. */\n  private _removeUniqueSelectionListener: () => void = () => {};\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnInit() {\n    this._removeUniqueSelectionListener = this._expansionDispatcher.listen(\n      (id: string, accordionId: string) => {\n        if (\n          this.accordion &&\n          !this.accordion.multi &&\n          this.accordion.id === accordionId &&\n          this.id !== id\n        ) {\n          this.expanded = false;\n        }\n      },\n    );\n\n    // When an accordion item is hosted in an accordion, subscribe to open/close events.\n    if (this.accordion) {\n      this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n    }\n  }\n\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.opened.complete();\n    this.closed.complete();\n    this.destroyed.emit();\n    this.destroyed.complete();\n    this._removeUniqueSelectionListener();\n    this._openCloseAllSubscription.unsubscribe();\n  }\n\n  /** Toggles the expanded state of the accordion item. */\n  toggle(): void {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to false. */\n  close(): void {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n\n  /** Sets the expanded state of the accordion item to true. */\n  open(): void {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n\n  private _subscribeToOpenCloseAllActions(): Subscription {\n    return this.accordion._openCloseAllActions.subscribe(expanded => {\n      // Only change expanded state if item is enabled\n      if (!this.disabled) {\n        this.expanded = expanded;\n      }\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CdkAccordion} from './accordion';\nimport {CdkAccordionItem} from './accordion-item';\n\n@NgModule({\n  imports: [CdkAccordion, CdkAccordionItem],\n  exports: [CdkAccordion, CdkAccordionItem],\n})\nexport class CdkAccordionModule {}\n"], "names": [], "mappings": ";;;;;;AAqBA;;;;AAIG;MACU,aAAa,GAAG,IAAI,cAAc,CAAe,cAAc;AAE5E;;AAEG;MAMU,YAAY,CAAA;;AAEd,IAAA,aAAa,GAAG,IAAI,OAAO,EAAiB;;AAG5C,IAAA,oBAAoB,GAAqB,IAAI,OAAO,EAAW;;IAG/D,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;;IAG5B,KAAK,GAAY,KAAK;;IAG5D,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC;;;;IAKxC,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;;AAGvC,IAAA,WAAW,CAAC,OAAsB,EAAA;AAChC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;IAGlC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AAC7B,QAAA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;;uGA/B3B,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,EAWJ,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAAA,gBAAgB,CAbxB,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAErD,YAAY,EAAA,UAAA,EAAA,CAAA;kBALxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;AACzC,oBAAA,QAAQ,EAAE,cAAc;oBACxB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAc,YAAA,EAAC,CAAC;AACjE,iBAAA;8BAYuC,KAAK,EAAA,CAAA;sBAA1C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;ACtBtC;;;AAGG;MAUU,gBAAgB,CAAA;AAC3B,IAAA,SAAS,GAAG,MAAM,CAAe,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAE;AAC1E,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC5C,IAAA,oBAAoB,GAAG,MAAM,CAAC,yBAAyB,CAAC;;AAG1D,IAAA,yBAAyB,GAAG,YAAY,CAAC,KAAK;;AAEnC,IAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ;;AAErD,IAAA,MAAM,GAAuB,IAAI,YAAY,EAAQ;;AAErD,IAAA,SAAS,GAAuB,IAAI,YAAY,EAAQ;AAE3E;;;;AAIG;AACgB,IAAA,cAAc,GAA0B,IAAI,YAAY,EAAW;;IAG7E,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC;;AAGxE,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,QAAiB,EAAA;;AAE5B,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;AAC/B,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;AACzB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;YAElC,IAAI,QAAQ,EAAE;AACZ,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAClB;;;AAGG;AACH,gBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;gBAChE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC;;iBACjD;AACL,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;;;;AAKpB,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;IAGlC,SAAS,GAAG,KAAK;;AAGzB,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,SAAS,EAAE;;IAEzB,IAAI,QAAQ,CAAC,KAAc,EAAA;AACzB,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;;AAEnB,IAAA,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;;AAGzB,IAAA,8BAA8B,GAAe,MAAK,GAAG;AAG7D,IAAA,WAAA,GAAA;IAEA,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CACpE,CAAC,EAAU,EAAE,WAAmB,KAAI;YAClC,IACE,IAAI,CAAC,SAAS;AACd,gBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK;AACrB,gBAAA,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,WAAW;AACjC,gBAAA,IAAI,CAAC,EAAE,KAAK,EAAE,EACd;AACA,gBAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;AAEzB,SAAC,CACF;;AAGD,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,+BAA+B,EAAE;;;;IAK3E,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtB,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AACrB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;QACzB,IAAI,CAAC,8BAA8B,EAAE;AACrC,QAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;;;IAI9C,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ;;;;IAKlC,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;;;IAKzB,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;;IAIhB,+BAA+B,GAAA;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,QAAQ,IAAG;;AAE9D,YAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;;AAE5B,SAAC,CAAC;;uGA/HO,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAyBR,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wCAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CA8BhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CA7DxB,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,QAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,SAAA,EAAA;;;AAGT,YAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC9C,SAAA,EAAA,QAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAT5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wCAAwC;AAClD,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,SAAS,EAAE;;;AAGT,wBAAA,EAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAC;AAC9C,qBAAA;AACF,iBAAA;wDASoB,MAAM,EAAA,CAAA;sBAAxB;gBAEkB,MAAM,EAAA,CAAA;sBAAxB;gBAEkB,SAAS,EAAA,CAAA;sBAA3B;gBAOkB,cAAc,EAAA,CAAA;sBAAhC;gBAOG,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBA+BhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;;MC7EzB,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAHnB,YAAY,EAAE,gBAAgB,CAC9B,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,gBAAgB,CAAA,EAAA,CAAA;wGAE7B,kBAAkB,EAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAJ9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AACzC,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;AAC1C,iBAAA;;;;;"}