// --- MAIN INPUT BAR STYLING ---

// :host <PERSON><PERSON><PERSON> thẻ <app-chat-input>
:host {
  display: block;
  flex-shrink: 0;
  padding: 0.5rem 1rem; // Giảm padding dọc
  background-color: #ffffff;
  border-top: 1px solid #e5e7eb;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative; // C<PERSON>n thiết để các popup định vị chính xác
}

.action-button {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  color: #6b7280;
  font-size: 1.35rem; // Tăng kích thước icon
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, color 0.2s;

  &:hover {
    background-color: #f0f2f5;
  }
}

.message-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f0f2f5;
  border-radius: 1.5rem;
  padding: 0 0.5rem;
}

.message-input-field {
  flex: 1;
  border: none;
  outline: none;
  background: none;
  padding: 0.65rem 0.75rem; // Tăng padding để ô input cao hơn
  font-size: 0.95rem;
  color: #050505;

  &::placeholder {
    color: #6b7280;
  }
}

.send-button {
  color: var(--chat-theme-color, #7b42f6); // Dùng màu theme
}

// --- POPUPS STYLING ---

.actions-popup,
.custom-emoji-picker {
  position: absolute;
  bottom: calc(100% + 8px); // Cách thanh input 8px
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12); // Shadow đẹp hơn
  z-index: 40;
  overflow: hidden;
  animation: popUp 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275); // Animation nảy lên
  transform-origin: bottom left;
}

// Popup cho nút '+'
.actions-popup {
  left: 0.5rem; // Căn theo nút +
  width: min(220px, 80vw);
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  gap: 0.25rem;

  // Mobile responsive
  @media (max-width: 768px) {
    width: min(200px, 85vw);
    left: 0.25rem;
  }
}

.action-popup-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  border-radius: 0.5rem;
  border: none;
  background: none;
  text-align: left;
  width: 100%;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f0f2f5;
  }

  i {
    font-size: 1.25rem;
    color: #333;
    width: 24px;
    text-align: center;
  }

  span {
    font-size: 0.95rem;
    font-weight: 500;
    color: #050505;
  }
}

// Popup cho emoji
.custom-emoji-picker {
  left: auto;
  right: 2.5rem; // Căn theo nút emoji
  transform-origin: bottom right;
  width: min(350px, 90vw);
  height: min(400px, 60vh);
  display: flex;
  flex-direction: column;

  // Mobile responsive
  @media (max-width: 768px) {
    right: 0.5rem;
    width: min(320px, 95vw);
    height: min(350px, 50vh);
  }
}

.emoji-search-bar {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid transparent;
    border-radius: 999px;
    background-color: #f0f2f5;
    outline: none;
    font-size: 0.9rem;
    &:focus {
      background-color: #ffffff;
      border-color: var(--chat-theme-color, #7b42f6);
    }
  }
}

.emoji-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0.5rem;
}

.emoji-category-title {
  padding: 0.75rem 0.25rem 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.1rem;
}

.emoji-item {
  font-size: 1.75rem;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.15s ease;
  aspect-ratio: 1 / 1;

  &:hover {
    background-color: #e5e7eb;
  }
}

.emoji-tabs {
  display: flex;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.emoji-tab {
  flex: 1;
  padding: 0.6rem 0;
  font-size: 1.3rem;
  border: none;
  background: none;
  cursor: pointer;
  color: #6b7280;
  position: relative;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f0f2f5;
  }

  &.active {
    color: var(--chat-theme-color, #7b42f6);
    background-color: #e9e3f8; // Màu nền nhẹ cho tab active
  }
}

// Animation
@keyframes popUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
