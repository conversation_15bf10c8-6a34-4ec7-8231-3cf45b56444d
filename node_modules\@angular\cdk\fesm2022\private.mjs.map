{"version": 3, "file": "private.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/private/visually-hidden/visually-hidden.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\n@Component({\n  styleUrl: 'visually-hidden.css',\n  exportAs: 'cdkVisuallyHidden',\n  encapsulation: ViewEncapsulation.None,\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class _VisuallyHiddenLoader {}\n"], "names": [], "mappings": ";;;;AAUA;;;AAGG;MAQU,qBAAqB,CAAA;uGAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,yGAHtB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,oQAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGD,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAPjC,SAAS;+BAEE,mBAAmB,EAAA,aAAA,EACd,iBAAiB,CAAC,IAAI,YAC3B,EAAE,EAAA,eAAA,EACK,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,oQAAA,CAAA,EAAA;;;;;"}