{"version": 3, "file": "layout.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/layout/layout-module.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/layout/breakpoints.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {NgModule} from '@angular/core';\n\n@NgModule({})\nexport class LayoutModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nexport const Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n\n  Handset:\n    '(max-width: 599.98px) and (orientation: portrait), ' +\n    '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet:\n    '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' +\n    '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web:\n    '(min-width: 840px) and (orientation: portrait), ' +\n    '(min-width: 1280px) and (orientation: landscape)',\n\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n"], "names": [], "mappings": ";;;;;;;;;MAUa,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAZ,YAAY,EAAA,CAAA;wGAAZ,YAAY,EAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,QAAQ;mBAAC,EAAE;;;ACFZ;AACA;AACa,MAAA,WAAW,GAAG;AACzB,IAAA,MAAM,EAAE,uBAAuB;AAC/B,IAAA,KAAK,EAAE,8CAA8C;AACrD,IAAA,MAAM,EAAE,+CAA+C;AACvD,IAAA,KAAK,EAAE,gDAAgD;AACvD,IAAA,MAAM,EAAE,qBAAqB;AAE7B,IAAA,OAAO,EACL,qDAAqD;QACrD,oDAAoD;AACtD,IAAA,MAAM,EACJ,4EAA4E;QAC5E,4EAA4E;AAC9E,IAAA,GAAG,EACD,kDAAkD;QAClD,kDAAkD;AAEpD,IAAA,eAAe,EAAE,mDAAmD;AACpE,IAAA,cAAc,EAAE,0EAA0E;AAC1F,IAAA,WAAW,EAAE,gDAAgD;AAE7D,IAAA,gBAAgB,EAAE,oDAAoD;AACtE,IAAA,eAAe,EAAE,4EAA4E;AAC7F,IAAA,YAAY,EAAE,kDAAkD;;;;;"}