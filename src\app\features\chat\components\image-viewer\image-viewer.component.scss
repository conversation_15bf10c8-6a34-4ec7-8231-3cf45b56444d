.image-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.image-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.image-viewer-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  z-index: 10;
}

.image-counter {
  color: white;
  font-size: 1rem;
  font-weight: 500;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  backdrop-filter: blur(10px);
}

.close-button {
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  backdrop-filter: blur(10px);

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }

  i {
    font-size: 1.2rem;
  }
}

.image-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.2s;
  z-index: 10;
  backdrop-filter: blur(10px);

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: translateY(-50%) scale(1.1);
  }

  &.nav-previous {
    left: 2rem;
  }

  &.nav-next {
    right: 2rem;
  }

  i {
    font-size: 1.2rem;
  }
}

.image-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.viewer-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  transition: transform 0.2s ease-out;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.image-viewer-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  backdrop-filter: blur(10px);
}

.control-button {
  background: none;
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  i {
    font-size: 0.9rem;
  }
}

.zoom-level {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 50px;
  text-align: center;
}

.thumbnail-strip {
  display: flex;
  gap: 0.5rem;
  max-width: 100%;
  overflow-x: auto;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 1rem;
  backdrop-filter: blur(10px);

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
}

.thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s, transform 0.2s;

  &:hover {
    transform: scale(1.05);
  }

  &.active {
    border-color: #3b82f6;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .image-viewer-header,
  .image-viewer-controls {
    padding: 1rem;
  }

  .nav-button {
    width: 40px;
    height: 40px;

    &.nav-previous {
      left: 1rem;
    }

    &.nav-next {
      right: 1rem;
    }
  }

  .viewer-image {
    max-width: 95%;
    max-height: 85%;
  }

  .thumbnail {
    width: 50px;
    height: 50px;
  }

  .zoom-controls {
    padding: 0.375rem 0.75rem;
  }

  .control-button {
    width: 28px;
    height: 28px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
