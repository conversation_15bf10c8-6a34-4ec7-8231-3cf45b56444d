{"version": 3, "file": "dispose-view-repeater-strategy-Cvpav0PR.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/collections/dispose-view-repeater-strategy.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  EmbeddedViewRef,\n  IterableChangeRecord,\n  IterableChanges,\n  ViewContainerRef,\n} from '@angular/core';\nimport {\n  _ViewRepeater,\n  _ViewRepeaterItemChanged,\n  _ViewRepeaterItemContext,\n  _ViewRepeaterItemContextFactory,\n  _ViewRepeaterItemValueResolver,\n  _ViewRepeaterOperation,\n} from './view-repeater';\n\n/**\n * A repeater that destroys views when they are removed from a\n * `ViewContainerRef`. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nexport class _DisposeViewRepeaterStrategy<T, R, C extends _ViewRepeaterItemContext<T>>\n  implements _ViewRepeater<T, R, C>\n{\n  applyChanges(\n    changes: IterableChanges<R>,\n    viewContainerRef: ViewContainerRef,\n    itemContextFactory: _ViewRepeaterItemContextFactory<T, R, C>,\n    itemValueResolver: _ViewRepeaterItemValueResolver<T, R>,\n    itemViewChanged?: _ViewRepeaterItemChanged<R, C>,\n  ) {\n    changes.forEachOperation(\n      (\n        record: IterableChangeRecord<R>,\n        adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => {\n        let view: EmbeddedViewRef<C> | undefined;\n        let operation: _ViewRepeaterOperation;\n        if (record.previousIndex == null) {\n          const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n          view = viewContainerRef.createEmbeddedView(\n            insertContext.templateRef,\n            insertContext.context,\n            insertContext.index,\n          );\n          operation = _ViewRepeaterOperation.INSERTED;\n        } else if (currentIndex == null) {\n          viewContainerRef.remove(adjustedPreviousIndex!);\n          operation = _ViewRepeaterOperation.REMOVED;\n        } else {\n          view = viewContainerRef.get(adjustedPreviousIndex!) as EmbeddedViewRef<C>;\n          viewContainerRef.move(view!, currentIndex);\n          operation = _ViewRepeaterOperation.MOVED;\n        }\n\n        if (itemViewChanged) {\n          itemViewChanged({\n            context: view?.context,\n            operation,\n            record,\n          });\n        }\n      },\n    );\n  }\n\n  detach() {}\n}\n"], "names": [], "mappings": ";;AAuBA;;;;;;;;AAQG;MACU,4BAA4B,CAAA;IAGvC,YAAY,CACV,OAA2B,EAC3B,gBAAkC,EAClC,kBAA4D,EAC5D,iBAAuD,EACvD,eAAgD,EAAA;QAEhD,OAAO,CAAC,gBAAgB,CACtB,CACE,MAA+B,EAC/B,qBAAoC,EACpC,YAA2B,KACzB;AACF,YAAA,IAAI,IAAoC;AACxC,YAAA,IAAI,SAAiC;AACrC,YAAA,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE;gBAChC,MAAM,aAAa,GAAG,kBAAkB,CAAC,MAAM,EAAE,qBAAqB,EAAE,YAAY,CAAC;AACrF,gBAAA,IAAI,GAAG,gBAAgB,CAAC,kBAAkB,CACxC,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,OAAO,EACrB,aAAa,CAAC,KAAK,CACpB;AACD,gBAAA,SAAS,GAAG,sBAAsB,CAAC,QAAQ;;AACtC,iBAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AAC/B,gBAAA,gBAAgB,CAAC,MAAM,CAAC,qBAAsB,CAAC;AAC/C,gBAAA,SAAS,GAAG,sBAAsB,CAAC,OAAO;;iBACrC;AACL,gBAAA,IAAI,GAAG,gBAAgB,CAAC,GAAG,CAAC,qBAAsB,CAAuB;AACzE,gBAAA,gBAAgB,CAAC,IAAI,CAAC,IAAK,EAAE,YAAY,CAAC;AAC1C,gBAAA,SAAS,GAAG,sBAAsB,CAAC,KAAK;;YAG1C,IAAI,eAAe,EAAE;AACnB,gBAAA,eAAe,CAAC;oBACd,OAAO,EAAE,IAAI,EAAE,OAAO;oBACtB,SAAS;oBACT,MAAM;AACP,iBAAA,CAAC;;AAEN,SAAC,CACF;;AAGH,IAAA,MAAM;AACP;;;;"}