import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRendering,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell,
  withRoutes
} from "./chunk-LT4HAYHU.js";
import "./chunk-WNXERYBG.js";
import "./chunk-QL3HCYBR.js";
import "./chunk-AAAFWXB2.js";
import "./chunk-EFKPQJBX.js";
import "./chunk-F3OVQXGI.js";
import "./chunk-EIZDCLGV.js";
import "./chunk-43KPLV43.js";
import "./chunk-2XLRDDJW.js";
import "./chunk-TXGYY7YM.js";
import "./chunk-6DU2HRTW.js";
export {
  AngularAppEngine,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  provideServerRendering,
  withAppShell,
  withRoutes,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
