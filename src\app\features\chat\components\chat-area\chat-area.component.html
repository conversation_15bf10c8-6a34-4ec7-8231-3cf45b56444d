<div class="chat-content-area" [style.--chat-theme-color]="themeColor$ | async">
    <app-chat-header [user]="(selectedUser$ | async)" (toggleInfoSidebar)="toggleInfoSidebar.emit()">
    </app-chat-header>

    <div class="message-list-container">
        <app-chat-message *ngFor="let msg of messages; let i = index" [message]="msg" [currentUser]="currentUser"
            (vote)="handleVote($event)" (openReactionPicker)="handleOpenReactionPicker(i, $event)"
            (openOptionsMenu)="handleOpenOptionsMenu(msg, $event)" (imageClick)="handleImageClick($event)">
        </app-chat-message>
    </div>

    <app-chat-input (sendMessage)="handleSendMessage($event)" (sendFile)="handleSendFile($event)"
        (sendImages)="handleSendImages($event)" (recordAudio)="handleRecordAudio()" (openPollModal)="openPollModal()"
        (openGifModal)="openGifModal()">
    </app-chat-input>
</div>

<div *ngIf="pickerOpenForMessageIndex !== null" class="modal-overlay" (click)="pickerOpenForMessageIndex = null">
    <div (click)="$event.stopPropagation()" class="reaction-picker modal-content">
        <span (click)="selectReaction(pickerOpenForMessageIndex, '❤️')" class="reaction-emoji">❤️</span>
        <span (click)="selectReaction(pickerOpenForMessageIndex, '😂')" class="reaction-emoji">😂</span>
        <span (click)="selectReaction(pickerOpenForMessageIndex, '👍')" class="reaction-emoji">👍</span>
        <span (click)="selectReaction(pickerOpenForMessageIndex, '😮')" class="reaction-emoji">😮</span>
        <span (click)="selectReaction(pickerOpenForMessageIndex, '😢')" class="reaction-emoji">😢</span>
        <span (click)="selectReaction(pickerOpenForMessageIndex, '😡')" class="reaction-emoji">😡</span>
    </div>
</div>

<div *ngIf="isOptionsMenuModalVisible" class="modal-overlay" (click)="closeOptionsMenuModal()">
    <div class="modal-content options-modal" (click)="$event.stopPropagation()">
        <ng-container *ngIf="selectedMessageForOptions as selectedMsg">
            <p *ngIf="selectedMsg.type === 'text'" class="text-sm bg-gray-100 p-3 rounded-md mb-4 break-words">"{{
                selectedMsg.text }}"</p>
        </ng-container>
        <div class="flex flex-col">
            <button (click)="forwardMessage()" class="menu-item"><i class="fa-solid fa-reply w-5"></i><span>Chuyển
                    tiếp</span></button>
            <button (click)="deleteMessage()" class="menu-item danger"><i
                    class="fa-solid fa-trash-can w-5"></i><span>Xóa tin nhắn</span></button>
        </div>
        <button (click)="closeOptionsMenuModal()"
            class="mt-4 w-full p-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-sm font-semibold">Hủy</button>
    </div>
</div>

<div *ngIf="isPollModalVisible" class="modal-overlay" (click)="closePollModal()">
    <div class="modal-content poll-creation-modal" (click)="$event.stopPropagation()">
        <h3 class="font-bold text-lg mb-4">Tạo bình chọn mới</h3>

        <input [(ngModel)]="pollQuestion" type="text" placeholder="Câu hỏi bình chọn"
            class="w-full p-2 border rounded-md mb-3">

        <div class="poll-options-list">
            <div *ngFor="let option of pollOptions; let i = index" class="flex items-center gap-2 mt-2">
                <input [(ngModel)]="option.text" type="text" [placeholder]="'Lựa chọn ' + (i + 1)"
                    class="flex-grow p-2 border rounded-md">
                <button *ngIf="pollOptions.length > 2" (click)="removePollOption(i)" class="remove-option-btn">
                    &times;
                </button>
            </div>
        </div>

        <button (click)="addPollOption()" class="add-option-btn" style="color: blue; margin-top: 10px">
            + Thêm lựa chọn
        </button>

        <div class="flex gap-2 mt-6">
            <button (click)="closePollModal()" class="w-full p-2 bg-gray-200 hover:bg-gray-300 rounded-lg">Hủy</button>
            <button (click)="createPoll()" class="w-full p-2 text-white hover:opacity-90 rounded-lg"
                [style.backgroundColor]="themeColor$ | async">Tạo</button>
        </div>
    </div>
</div>

<div *ngIf="isGifModalVisible" class="modal-overlay" (click)="closeGifModal()">
    <div class="modal-content gif-modal" (click)="$event.stopPropagation()">
        <h3 class="font-bold text-lg mb-4">Chọn một ảnh GIF</h3>
        <input [(ngModel)]="gifSearchTerm" (input)="searchGifs()" type="text" placeholder="Tìm kiếm GIF..."
            class="w-full p-2 border rounded-md mb-4">
        <div class="gif-grid">
            <div *ngIf="isLoadingGifs" class="text-center col-span-full py-8">
                <i class="fas fa-spinner fa-spin text-2xl text-gray-500"></i>
                <p class="mt-2 text-gray-600">Đang tải GIF...</p>
            </div>
            <div *ngIf="!isLoadingGifs && gifResults.length === 0" class="text-center col-span-full py-8">
                <i class="fas fa-search text-2xl text-gray-400"></i>
                <p class="mt-2 text-gray-600">Không tìm thấy GIF nào</p>
            </div>
            <img *ngFor="let gif of gifResults" [src]="gif.preview_url || gif.url" [alt]="gif.title"
                (click)="selectGif(gif)"
                class="cursor-pointer rounded-md hover:opacity-80 hover:scale-105 transition-all duration-200 object-cover w-full h-32" />
        </div>
    </div>
</div>

<!-- Image Viewer -->
<app-image-viewer [images]="imageViewerImages" [currentIndex]="imageViewerCurrentIndex"
    [isVisible]="isImageViewerVisible" (close)="closeImageViewer()" (indexChange)="onImageViewerIndexChange($event)">
</app-image-viewer>