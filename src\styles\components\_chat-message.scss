/* Styles for a single message bubble and its contents */

.message-wrapper {
  margin-bottom: 8px; // Default spacing between messages

  // Reduced spacing for consecutive messages from same sender
  &:not(:first-child) {
    margin-top: 2px;
  }

  // Increased spacing when sender changes (different alignment indicates different sender)
  &.justify-end + .justify-start,
  &.justify-start + .justify-end {
    margin-top: 16px;
  }

  // Special spacing for poll messages
  &.justify-center {
    margin: 16px 0;
  }
}

.message-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;

  .message-wrapper:hover & {
    opacity: 1;
  }
}
// Removed old standard-bubble styles - using new modern colors below
.action-button {
  background-color: #f0f2f5;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #606770;
  border: none;
  transition: background-color 0.2s;

  &:hover {
    background-color: #e4e6e9;
  }
}

.message-bubble {
  border-radius: 1.125rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  backdrop-filter: blur(10px);

  &.incoming {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #1e293b;
    border-bottom-left-radius: 0.375rem;
  }

  &.outgoing {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border-bottom-right-radius: 0.375rem;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);

    // Alternative modern gradient options (comment/uncomment to try different styles)
    // background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%); // Indigo
    // background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); // Purple
    // background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); // Cyan
    // background: linear-gradient(135deg, #10b981 0%, #059669 100%); // Emerald
  }
}

.reaction-display {
  position: absolute;
  bottom: -10px;
  background-color: #f0f2f5;
  border: 1px solid #fff;
  font-size: 0.75rem;
  border-radius: 1rem;
  padding: 1px 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.image-grid {
  display: grid;
  gap: 6px;
  border-radius: 12px;
  overflow: hidden;
  max-width: 100%;

  // Layout cho 1 ảnh
  &.single {
    grid-template-columns: 1fr;
    max-width: min(280px, 90vw);

    img {
      max-height: min(300px, 40vh);
      aspect-ratio: auto;
      object-fit: cover;
      border-radius: 12px;
      width: 100%;
    }
  }

  // Layout cho 2 ảnh
  &:not(.single):not(:has(img:nth-child(3))) {
    grid-template-columns: repeat(2, 1fr);
    max-width: min(320px, 90vw);

    img {
      aspect-ratio: 1 / 1;
      object-fit: cover;
      height: min(150px, 20vw);
      width: 100%;
    }
  }

  // Layout cho 3 ảnh
  &:has(img:nth-child(3)):not(:has(img:nth-child(4))) {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    max-width: min(320px, 90vw);
    height: min(300px, 40vw);

    img:first-child {
      grid-row: 1 / 3;
      height: 100%;
      aspect-ratio: 1 / 1;
      object-fit: cover;
    }

    img:nth-child(2),
    img:nth-child(3) {
      height: min(145px, 19vw);
      aspect-ratio: 1 / 1;
      object-fit: cover;
    }
  }

  // Layout cho 4+ ảnh
  &:has(img:nth-child(4)) {
    grid-template-columns: repeat(2, 1fr);
    max-width: min(320px, 90vw);

    img {
      aspect-ratio: 1 / 1;
      object-fit: cover;
      height: min(150px, 20vw);
      width: 100%;
    }

    // Hiển thị tối đa 4 ảnh, ảnh thứ 4 có overlay "+X" nếu có nhiều hơn
    img:nth-child(n + 5) {
      display: none;
    }
  }

  img {
    transition: transform 0.2s ease, filter 0.2s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.02);
      filter: brightness(0.95);
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    gap: 4px;

    &.single {
      max-width: 85vw;

      img {
        max-height: 35vh;
        border-radius: 8px;
      }
    }

    &:not(.single) {
      max-width: 85vw;

      img {
        height: min(120px, 18vw);
        border-radius: 6px;
      }
    }

    &:has(img:nth-child(3)):not(:has(img:nth-child(4))) {
      height: min(240px, 36vw);

      img:nth-child(2),
      img:nth-child(3) {
        height: min(115px, 17vw);
      }
    }
  }
}

audio {
  height: 40px;
}

.poll-bubble {
  width: 100%;
  max-width: 400px;
  padding: 1rem;
  background-color: #fff;
  border: 1px solid #e4e6e9;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.poll-option {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced0d4;
  background-color: #f0f2f5;
  border-radius: 0.5rem;
  text-align: left;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: background-color 0.2s, border-color 0.2s;
  color: #050505;

  &:hover {
    border-color: #a4a7ab;
  }

  &.voted {
    border-color: #7b42f6;
    background-color: #f0eaff;
  }

  .poll-bar {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: #d8c8fa;
    z-index: 0;
    transition: width 0.3s ease-in-out;
  }

  > div {
    position: relative;
    z-index: 1;
  }
}

// Mobile responsive cho message wrapper
@media (max-width: 768px) {
  .message-wrapper {
    .flex.items-center.space-x-2 {
      max-width: 95% !important;
    }
  }

  .message-bubble {
    &.incoming,
    &.outgoing {
      border-radius: 1rem;

      &.incoming {
        border-bottom-left-radius: 0.25rem;
      }

      &.outgoing {
        border-bottom-right-radius: 0.25rem;
      }
    }
  }

  .action-button {
    width: 28px;
    height: 28px;
    font-size: 0.875rem;
  }
}
